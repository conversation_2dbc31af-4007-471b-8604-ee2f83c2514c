"use client";
import React, { useState, useEffect } from "react";
import { Eye, EyeOff, Mail, Lock, ChevronRight } from "lucide-react";
import Image from "next/image";

import {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { usefulLinks, connectLinks, weAreAtUCW, studyAtUCW } from "./constant";
import { useRouter } from "next/navigation";
import { useFormContext } from "react-hook-form";
import DynamicFields from "@/components/custom/DynamicFields";
import { sortOrder } from "@/helpers/Sorting";
import loader from "../../../public/loader.svg";
import { useRecaptcha } from "@/hooks/useRecaptcha";
import { verifyRecaptcha } from "@/api/api";

interface LoginScreenProps {
  handleLogin: () => void;
  formQuery: any;
  errorMessage?: string;
  setErrorMessage: (message: string) => void;
  isRecaptchaEnabled: boolean;
}

export default function UcwLoginScreen({
  handleLogin,
  formQuery,
  errorMessage,
  setErrorMessage,
  isRecaptchaEnabled,
}: LoginScreenProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isActiveCurosel, setIsActiveCurosel] = useState(0);
  const [api, setApi] = useState<CarouselApi>();
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);

  const {
    register,
    setValue,
    watch,
    clearErrors,
    formState: { errors },
    trigger,
    setError,
  } = useFormContext();

  const { executeRecaptcha, isRecaptchaLoaded, recaptchaError } =
    useRecaptcha();

  useEffect(() => {
    if (errorMessage) {
      setIsLoading(false);
    }
  }, [errorMessage]);

  useEffect(() => {
    if (!api) {
      return;
    }

    setIsActiveCurosel(api.selectedScrollSnap());
    setCanScrollPrev(api.canScrollPrev());
    setCanScrollNext(api.canScrollNext());

    api.on("select", () => {
      setIsActiveCurosel(api.selectedScrollSnap());
      setCanScrollPrev(api.canScrollPrev());
      setCanScrollNext(api.canScrollNext());
    });
  }, [api]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const fieldNames = formQuery?.fieldData
      ?.filter((item: any) => item?.indexOrder <= 2)
      ?.map((item: any) => item?.fieldName);

    const isValid = await trigger(fieldNames);

    if (!isValid) {
      return;
    }

    setIsLoading(true);

    // Check for reCAPTCHA errors first
    if (isRecaptchaEnabled && recaptchaError) {
      setIsLoading(false);
      console.error("reCAPTCHA error:", recaptchaError);
      return;
    }

    try {
      if (isRecaptchaEnabled) {
        // Execute reCAPTCHA verification
        const recaptchaToken = await executeRecaptcha("login");
        if (!recaptchaToken) {
          setIsLoading(false);
          console.error("reCAPTCHA verification failed");
          return;
        }

        // Verify reCAPTCHA token
        const recaptchaResult = await verifyRecaptcha(recaptchaToken, "login");
        if (!recaptchaResult.success) {
          setIsLoading(false);
          setErrorMessage(
            recaptchaResult?.error || "reCAPTCHA verification failed",
          );
          console.error(
            "reCAPTCHA verification failed:",
            recaptchaResult.error,
          );
          return;
        }
      }

      // Proceed with login if reCAPTCHA is successful
      await handleLogin();
    } catch (error) {
      console.error("Login error:", error);
      setIsLoading(false);
    }
  };

  return (
    <div className="h-screen w-full overflow-y-scroll">
      <div className="min-h-screen flex flex-col min-w-full max-sm:min-w-0 max-sm:block">
        {/* Header */}
        <header className="py-4 px-4 sm:px-6 md:px-8 lg:px-16 w-full pt-8 max-sm:px-2 max-sm:py-2 max-sm:flex max-sm:flex-row max-sm:items-center max-sm:gap-2">
          <div className="flex items-center justify-between w-full max-sm:flex-row max-sm:items-center max-sm:w-full max-sm:gap-2">
            {/* Logo */}
            <Image
              src="/ucwlogin/ucw-logo-1.png"
              alt="University Canada West"
              width={180}
              height={50}
              className="h-10 sm:h-12 w-auto object-contain"
            />
          </div>
        </header>

         {/* Main content */}
        <div className="flex flex-col lg:flex-row px-5 sm:px-6 md:px-8 lg:px-16 pt-2 sm:pt-8 lg:pt-8 pb-6 w-full">
          <div className="border border-black  overflow-hidden flex flex-col lg:flex-row w-full">
            {/* Left Panel - University Branding */}
            <div className="w-full lg:w-[42%] bg-[url('/ucwlogin/login.png')]  bg-cover bg-center text-white pt-3">
              <div className="h-full flex flex-col justify-center px-2 sm:px-3 md:px-4 lg:px-5 py-4 sm:py-6 md:py-7 lg:py-8">
                <div className="max-w-md mx-auto w-full">
                  <h1 className="text-xs sm:text-sm md:text-sm pb-3 sm:pb-5 font-bold">
                    Start Your Future at University Canada West
                  </h1>
                  <p className="text-xs sm:text-sm text-gray-300 mb-6 sm:mb-8">
                    Your journey begins here. UCW is where ambition meets
                    opportunity - in the heart of one of the world&apos;s most
                    livable cities.
                  </p>

                  <h2 className="text-xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4">
                    Why Choose UCW
                  </h2>
                  
                   <div className="space-y-3 mb-6 sm:mb-8">
                    <div className="bg-white/50 backdrop-blur-sm rounded-lg p-3 sm:p-4 md:p-6">
                      <div className="flex items-center p-2 sm:p-3 border-b border-white/30">
                        <p className="text-base sm:text-lg md:text-xl font-bold text-white w-16 sm:w-20">
                           93%
                        </p>
                        <p className="text-xs sm:text-sm text-white flex-1 ml-4 sm:ml-6">
                          MBA alumni employment rate
                        </p>
                      </div>
                      <div className="flex items-center p-2 sm:p-3 border-b border-white/30">
                        <div className="text-base sm:text-lg md:text-xl font-bold text-white w-16 sm:w-20">
                         92%
                        </div>
                        <div className="text-xs sm:text-sm text-white flex-1 ml-4 sm:ml-6">
                       graduation success rate
                        </div>
                      </div>
                      <div className="flex items-center p-2 sm:p-3 border-b border-white/30">
                        <div className="text-base sm:text-lg md:text-xl font-bold text-white w-16 sm:w-20">
                          11,000+
                        </div>
                        <div className="text-xs sm:text-sm text-white flex-1 ml-4 sm:ml-6">
                      students from 100+ countries
                        </div>
                      </div>
                      <div className="flex items-center p-2 sm:p-3">
                        <div className="text-base sm:text-lg md:text-xl font-bold text-white w-16 sm:w-20">
                          <div className="flex items-center gap-2 ">  <p>5</p> <Image
                            src={"/ucwlogin/star.png"}
                            alt="star"
                            width={30}
                            height={18}
                            className="w-4 sm:w-5"
                          /></div>
                        </div>
                        <div className="text-xs sm:text-sm text-white flex-1 ml-4 sm:ml-6">
                           5 stars QS Star rated institution
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white rounded-lg p-2 sm:p-3 md:p-6">
                    <div className="flex items-center p-2 sm:p-3">
                      <Image
                        src={"/ucwlogin/batch.png"}
                        alt="award"
                        width={350}
                        height={150}
                        className="w-full h-auto max-w-[300px] sm:max-w-[350px]"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Panel - Login Form */}
            <div className="w-full lg:w-[57%] flex items-center justify-center bg-white px-2 sm:px-3 md:px-4 lg:pl-5 xl:pl-7 2xl:pl-10 py-4 lg:py-0">
              <div className="w-full max-w-md lg:w-[54%] lg:max-w-md">
                <div className="text-center mb-4 sm:mb-6">
                  <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">
                    Welcome to the UCW Application Portal
                  </h2>
                  <p className="text-sm sm:text-base text-gray-600">
                    Log in to begin or continue your application.
                  </p>
                </div>

                <form onSubmit={handleSubmit} className="flex flex-col gap-4">
                  {sortOrder(formQuery?.fieldData, "indexOrder")
                    ?.filter((item: any) => item?.indexOrder <= 2)
                    ?.map((item: any, index: any) => (
                      <div key={index} className="flex flex-col">
                        <DynamicFields
                          register={register}
                          selectedValue={
                            watch(item?.fieldName) ||
                            watch(`${item?.documentType}`) ||
                            ""
                          }
                          disabled={
                            item?.disabledWhen
                              ? watch(item?.disabledWhen?.fieldName)?.label ===
                                item?.disabledWhen?.value
                              : false
                          }
                          isVisibleWhen
                          fieldItem={item}
                          label={
                            item?.label ||
                            item?.displayName ||
                            item?.placeholder
                          }
                          handleValueChanged={(value: any, type?: string) => {
                            if (item?.childField && item?.setValue) {
                              if (value?.value == item?.value) {
                                setValue(item?.childField, item?.setValue);
                                clearErrors(item?.childField);
                              } else {
                                setValue(item?.childField, "");
                              }
                            }
                            clearErrors(item?.fieldName);
                            clearErrors(`${item?.documentType}`);
                            if (type === "pickList" && item?.fieldDisplayName) {
                              setValue(item?.fieldDisplayName, value);
                            }
                            if (item?.resetChild) {
                              setValue(item?.resetChild, "");
                              clearErrors(item?.resetChild);
                            }
                            setValue(item?.fieldName, value);
                          }}
                          errorMessage={
                            errors?.[item?.fieldName]?.message ||
                            errors?.[`${item?.documentType}`]?.message
                          }
                          name={item?.fieldName}
                          trigger={trigger}
                          watch={watch}
                          clearErrors={clearErrors}
                          setError={setError}
                          displayNoTitle={false}
                          setValue={setValue}
                        />
                      </div>
                    ))}

                  {errorMessage && (
                    <div className="text-red-600 text-sm mt-2">
                      {errorMessage}
                    </div>
                  )}

                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-red-600 text-white py-3 px-4 hover:bg-red-700 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed mt-2"
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center">
                        <Image
                          priority
                          src={loader}
                          height={32}
                          width={32}
                          alt="Loading..."
                        />
                      </div>
                    ) : (
                      "Login"
                    )}
                  </button>

                  <div className="flex justify-center items-center py-3">
                    <span className="text-gray-500 text-sm">or</span>
                  </div>

                  <button
                    type="button"
                    onClick={() => router.push("/application")}
                    className="w-full bg-gray-800 text-white py-3 px-4  hover:bg-gray-900 focus:outline-none"
                  >
                    New User
                  </button>

                  <div className="border-t border-gray-200 my-4"></div>

                  <div className="text-center">
                    <span
                      onClick={() => router.push("/forgot-password")}
                      className="text-sm text-[#797A7A] cursor-pointer"
                    >
                      Forgot your password?
                    </span>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        {/* Contact section */}
      
         <div className="px-4 sm:px-6 md:px-8 lg:px-16 pt-6 sm:pt-8 lg:pt-10">
          <div className="flex flex-col md:flex-row items-center justify-between bg-[url('/ucwlogin/emergency-img.png')] bg-cover bg-center text-white p-4 sm:p-6 md:p-8 lg:p-12">
            <div className="text-center md:text-left mb-4 md:mb-0">
              <h3 className="text-lg sm:text-xl md:text-2xl font-bold mb-2 sm:mb-3">
                Got a Question? Let&apos;s Talk.
              </h3>
              <p className="text-sm md:text-base leading-relaxed">
                  Request a callback from our admissions team. We&apos;ll help you
                find the right path.
              </p>
              <div className="mt-3 sm:mt-4">
                <a
                  href="https://www.ucanwest.ca/contact-us"
                  target="_blank"
                  className="inline-block bg-red-600 text-white px-4 sm:px-6 py-2 text-sm hover:bg-red-700 transition-colors font-bold"
                >
                  Request a Call Back
                </a>
              </div>
            </div>
          </div>
        </div>
       

        <div className="w-full sm:w-11/12 md:w-9/12 mx-auto border-b-2 border-red-600 py-3 sm:py-4 md:py-8"></div>

        {/* Campus section */}
        <div className="pt-3 sm:pt-4 md:pt-8">
          <div className="container mx-auto">
            <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-center pb-2 mb-6 sm:mb-8">
              Study in the Heart of Vancouver
            </h2>
            <div className="flex flex-col items-center md:flex-row justify-center gap-6 md:gap-x-10">
              <div className="rounded-lg shadow-md overflow-hidden bg-[url('/ucwlogin/campus-1.png')] bg-cover bg-center w-full sm:w-[80%] md:w-[400px] h-[350px] sm:h-[400px]">
                <div className="p-4 sm:p-6 h-full flex flex-col justify-end bg-black/40">
                  <h3 className="font-bold text-white text-base sm:text-lg mb-2">
                    Vancouver House Campus
                  </h3>
                  <p className="text-xs sm:text-sm text-[#FFFFFF] mb-4 md:min-h-[96px]">
                    A modern learning environment inside one of Vancouver&apos;s
                    most iconic developments designed by world-renowned
                    architect Bjarke Ingels. Over 90,000 sq. ft of space
                    dedicated to innovation and inspiration.
                  </p>
                  <a
                    href="https://www.youtube.com/watch?v=GF-pCqm-MB8#"
                    target="_blank"
                    className="text-xs sm:text-sm text-white underline underline-offset-4 font-medium flex flex-row"
                  >
                    Get more info
                    <ChevronRight className="h-4 w-4 mt-1" />
                  </a>
                </div>
              </div>

              <div className="rounded-lg shadow-md overflow-hidden bg-[url('/ucwlogin/campus-2.png')] bg-cover bg-center w-full sm:w-[80%] md:w-[400px] h-[350px] sm:h-[400px]">
                <div className="p-4 sm:p-6 h-full flex flex-col justify-end bg-black/40">
                  <h3 className="font-bold text-white text-base sm:text-lg mb-2">
                    West Pender Campus
                  </h3>
                  <p className="text-xs sm:text-sm text-[#FFFFFF] mb-4 md:min-h-[96px]">
                    Located in a historic building in Vancouver&apos;s financial
                    district. Minutes from transit, restaurants and the
                    waterfront, this is where education meets city energy.
                  </p>
                  <a
                    href="https://www.instagram.com/university_canada_west/reel/DF_CX0Wyxp6/#"
                    target="_blank"
                    className="text-xs sm:text-sm text-white underline underline-offset-4 font-medium flex flex-row"
                  >
                    Get more info <ChevronRight className="h-4 w-4 mt-1 " />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="w-full sm:w-11/12 md:w-9/12 mx-auto border-b-2 border-red-600 py-3 sm:py-4 md:py-8"></div>

        {/* Testimonials section */}
        <div className="pt-4 sm:pt-6 md:pt-8 lg:pt-10 relative">
          <div className="container mx-auto px-4 sm:px-6 md:px-8">
            <div className="flex flex-col justify-center items-center max-w-6xl mx-auto">
              <h3 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-center mb-4 sm:mb-6 md:mb-8 max-w-4xl">
                Hear from Our Community
              </h3>
              <h2 className="text-sm sm:text-base md:text-lg text-center mb-8 sm:mb-10 md:mb-12 max-w-4xl text-gray-700 leading-relaxed">
                Discover what makes UCW more than just a university.
              </h2>
            </div>
          </div>

          <div className="w-full">
            <Carousel
              className="w-full"
              setApi={setApi}
              opts={{
                align: "start",
                loop: true,
              }}
            >
              <CarouselContent>
                <CarouselItem className="md:basis-full">
                  <div className="relative w-full h-[350px] sm:h-[450px] md:h-[550px] lg:h-[650px] 2xl:h-[1050px] overflow-hidden">
                    <Image
                      src="/ucwlogin/user-1.png"
                      alt="Student testimonial"
                      fill
                      className="object-cover object-center w-full h-full"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
                      priority
                      style={{
                        objectFit: "cover",
                        width: "100%",
                        height: "100%",
                      }}
                    />
                    <div className="absolute right-[8%] sm:right-[10%] md:right-[12%] lg:right-[15%] top-1/2 -translate-y-1/2 bg-white p-2 sm:p-4 md:p-6 rounded-xl sm:rounded-3xl shadow-lg w-[180px] sm:w-[320px] md:w-[380px] max-w-[calc(100vw-2rem)] mx-sm">
                      <h3 className="font-bold text-sm sm:text-lg mb-1 sm:mb-2">
                        José Cabal
                      </h3>
                      <p className="text-[10px] sm:text-sm text-gray-600 italic mb-2 sm:mb-4">
                        &quot;UCW&apos;s unwavering support and guidance nurtured my academic and professional growth... The exposure to Canadian business culture broadened my global perspective.&quot;
                      </p>
                      <div className="flex items-center">
                        <a
                          href="https://www.ucanwest.ca/testimonial/alumni/jose-cabal"
                          target="_blank"
                          className="text-[10px] sm:text-sm flex items-center gap-0.5 text-gray-600"
                        >
                          Read more <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4" />
                        </a>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
                <CarouselItem className="md:basis-full">
                  <div className="relative w-full h-[350px] sm:h-[450px] md:h-[550px] lg:h-[650px] 2xl:h-[1050px] overflow-hidden">
                    <Image
                      src="/ucwlogin/user-2.png"
                      alt="Student testimonial"
                      fill
                      className="object-cover object-center w-full h-full"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
                      style={{
                        objectFit: "cover",
                        width: "100%",
                        height: "100%",
                      }}
                    />
                    <div className="absolute right-[8%] sm:right-[10%] md:right-[12%] lg:right-[15%] top-1/2 -translate-y-1/2 bg-white p-2 sm:p-4 md:p-6 rounded-xl sm:rounded-3xl shadow-lg w-[180px] sm:w-[320px] md:w-[380px] max-w-[calc(100vw-2rem)] mx-sm">
                      <h3 className="font-bold text-sm sm:text-lg mb-1 sm:mb-2">
                        Alejandra Bonilla, Bachelor of Commerce Student
                      </h3>
                      <p className="text-[10px] sm:text-sm text-gray-600 italic mb-2 sm:mb-4">
                        &quot;UCW is shaping my career by providing me with practical tools, industry connections and the confidence to take on real-world challenges. &quot;
                      </p>
                      <div className="flex items-center">
                        <a
                          href="https://www.youtube.com/watch?v=th9yNPnwpkk&list=PLDRVt2h_hihbDuw87zyd4hf6Giqii1vct&index=5"
                          target="_blank"
                          className="text-[10px] sm:text-sm text-gray-600 flex items-center gap-0.5"
                        >
                          Read more <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4" />
                        </a>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
                <CarouselItem className="md:basis-full">
                  <div className="relative w-full h-[350px] sm:h-[450px] md:h-[550px] lg:h-[650px] 2xl:h-[1050px] overflow-hidden">
                    <Image
                      src="/ucwlogin/user-3.png"
                      alt="Student testimonial"
                      fill
                      className="object-cover object-center w-full h-full"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
                      priority
                      style={{
                        objectFit: "cover",
                        width: "100%",
                        height: "100%",
                      }}
                    />
                    <div className="absolute left-[8%] sm:left-[10%] md:left-[12%] lg:left-[15%] top-1/2 -translate-y-1/2 bg-white p-2 sm:p-4 md:p-6 rounded-xl sm:rounded-3xl shadow-lg w-[180px] sm:w-[320px] md:w-[380px] max-w-[calc(100vw-2rem)] mx-sm">
                      <h3 className="font-bold text-sm sm:text-lg">
                        Irene, MBA student from Nigeria
                      </h3>
                      <p className="text-[10px] sm:text-sm text-gray-600 italic my-1 sm:my-2">
                        &quot;I&apos;ve learned so much. I have made so many amazing friends from various cultural backgrounds and I think I just made one of the best decisions of my life.&quot;
                      </p>
                      <div className="flex items-center mt-1 sm:mt-2">
                        <a
                          href="https://www.youtube.com/watch?v=KBw99qeERZ4&list=PLDRVt2h_hihbDuw87zyd4hf6Giqii1vct&index=12"
                          target="_blank"
                          className="text-[10px] sm:text-sm text-gray-600 flex items-center gap-0.5"
                        >
                          Read more <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4" />
                        </a>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
                <CarouselItem className="md:basis-full">
                  <div className="relative w-full h-[350px] sm:h-[450px] md:h-[550px] lg:h-[650px] 2xl:h-[1050px] overflow-hidden">
                    <Image
                      src="/ucwlogin/user-4.png"
                      alt="Student testimonial"
                      fill
                      className="object-cover object-center w-full h-full"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
                      priority
                      style={{
                        objectFit: "cover",
                        width: "100%",
                        height: "100%",
                      }}
                    />
                    <div className="absolute left-[8%] sm:left-[10%] md:left-[12%] lg:left-[15%] top-1/2 -translate-y-1/2 bg-white p-2 sm:p-4 md:p-6 rounded-xl sm:rounded-3xl shadow-lg w-[180px] sm:w-[320px] md:w-[380px] max-w-[calc(100vw-2rem)] mx-sm">
                      <h3 className="font-bold text-sm sm:text-lg">
                        Suat, MBA student from Türkiye
                      </h3>
                      <p className="text-[10px] sm:text-sm text-gray-600 italic my-1 sm:my-2">
                        &quot;I would advise new students coming to UCW, not only to take the lessons but also to take part in extracurricular activities and join the networking events.&quot;
                      </p>
                      <div className="flex items-center mt-1 sm:mt-2">
                        <a
                          href="https://www.youtube.com/watch?v=dzuannrjR-8&list=PLDRVt2h_hihbDuw87zyd4hf6Giqii1vct&index=30"
                          target="_blank"
                          className="text-[10px] sm:text-sm text-gray-600 flex items-center gap-0.5"
                        >
                          Read more <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4" />
                        </a>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
                <CarouselItem className="md:basis-full">
                  <div className="relative w-full h-[350px] sm:h-[450px] md:h-[550px] lg:h-[650px] 2xl:h-[1050px] overflow-hidden">
                    <Image
                      src="/ucwlogin/user-5.png"
                      alt="Student testimonial"
                      fill
                      className="object-cover object-center w-full h-full"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
                      style={{
                        objectFit: "cover",
                        width: "100%",
                        height: "100%",
                      }}
                    />
                    <div className="absolute right-[8%] sm:right-[10%] md:right-[12%] lg:right-[15%] top-1/2 -translate-y-1/2 bg-white p-2 sm:p-4 md:p-6 rounded-xl sm:rounded-3xl shadow-lg w-[180px] sm:w-[320px] md:w-[380px] max-w-[calc(100vw-2rem)] mx-sm">
                      <h3 className="font-bold text-sm sm:text-lg mb-1 sm:mb-2">
                        Victoria Garcia, Bachelor of Arts in Business Communications from Colombia
                      </h3>
                      <p className="text-[10px] sm:text-sm text-gray-600 italic mb-2 sm:mb-4">
                        &quot;The quality of education is excellent, it has given me a strong foundation in both business principles and communication strategy skills that are essential across various industries.&quot;
                      </p>
                      <div className="flex items-center">
                        <a
                          href="https://www.youtube.com/watch?v=9op71lmhsuE"
                          target="_blank"
                          className="text-[10px] sm:text-sm flex items-center gap-0.5 text-gray-600"
                        >
                          Read more <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4" />
                        </a>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              </CarouselContent>

              <button
                onClick={() => api?.scrollPrev()}
                className={`absolute left-4 sm:left-8 md:left-12 lg:left-16 top-1/2 -translate-y-1/2 z-20 bg-transparent border-none cursor-pointer transition-opacity duration-200 max-sm:w-7 max-sm:h-7 max-sm:p-0.5 md:p-0.5 ${
                  !canScrollPrev
                    ? "opacity-0 pointer-events-none"
                    : "opacity-100"
                }`}
                aria-label="Previous slide"
              >
                <svg
                  width="59"
                  height="59"
                  viewBox="0 0 59 59"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="max-sm:w-7 max-sm:h-7"
                  style={{ transform: 'scaleX(-1)' }}
                >
                  <path
                    d="M30.975 29.5L19.6666 18.1917L23.1083 14.75L37.8583 29.5L23.1083 44.25L19.6666 40.8083L30.975 29.5Z"
                    fill="#FEF7FF"
                  />
                </svg>
              </button>

              <button
                onClick={() => api?.scrollNext()}
                className={`absolute right-4 sm:right-8 md:right-12 lg:right-16 top-1/2 -translate-y-1/2 z-20 bg-transparent border-none cursor-pointer transition-opacity duration-200 max-sm:w-7 max-sm:h-7 max-sm:p-0.5 max-sm:mx-2 md:p-0.5 mx-sm ${
                  !canScrollNext
                    ? "opacity-0 pointer-events-none"
                    : "opacity-100"
                }`}
                aria-label="Next slide"
              >
                <svg
                  width="59"
                  height="59"
                  viewBox="0 0 59 59"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="max-sm:w-7 max-sm:h-7"
                >
                  <path
                    d="M30.975 29.5L19.6666 18.1917L23.1083 14.75L37.8583 29.5L23.1083 44.25L19.6666 40.8083L30.975 29.5Z"
                    fill="#FEF7FF"
                  />
                </svg>
              </button>

              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 sm:space-x-3 z-20 justify-center items-center">
                {Array.from({ length: 5 }).map((_, index) => (
                  <span
                    key={index}
                    className={`h-2 w-2 sm:h-3 sm:w-3 rounded-full transition-colors duration-200 ${
                      isActiveCurosel === index ? "bg-red-600" : "bg-gray-300"
                    } `}
                  ></span>
                ))}
              </div>
            </Carousel>
          </div>
        </div>

        {/* Footer */}
        <footer className="bg-black text-white pt-10 sm:pt-16 md:pt-18 pb-8 sm:pb-12 md:pb-16 border-l-8 border-red-500">
          <div className=" conatiner max-auto px-12">
            <div className="flex flex-col md:flex-row flex-wrap justify-between gap-8 mb-8">
              <div className="lg:col-span-1">
                <Image
                  src="/ucwlogin/ucw-logo-2.png"
                  alt="University Canada West"
                  width={150}
                  height={40}
                  className="mb-4 sm:mb-6 max-w-full h-auto max-sm:max-w-[150px] max-sm:h-auto"
                />
                <p className="text-xs sm:text-sm text-white mb-2">
                  1461 Granville Street <br />
                  Vancouver, British Columbia <br />
                  V6Z 0E5, Canada
                </p>
              </div>

              <div className="lg:col-span-1">
                <h4 className="font-bold text-base sm:text-lg mb-4">
                  Useful Links
                </h4>
                <ul className="flex flex-col gap-2 sm:gap-4 text-xs sm:text-sm text-white">
                  {usefulLinks.map((link, index) => (
                    <li key={index}>
                      <a
                        href={link.href}
                        target="_blank"
                        className="hover:text-red-600"
                      >
                        {link.title}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="lg:col-span-1">
                <h4 className="font-bold text-base sm:text-lg mb-4">Contact</h4>
                <ul className="flex flex-col gap-2 sm:gap-4 text-xs sm:text-sm text-white">
                  {connectLinks.map((link, index) => (
                    <li key={index}>
                      <a
                        href={link.href}
                        target="_blank"
                        className="hover:text-red-600"
                      >
                        {link.title}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="lg:col-span-1">
                <h4 className="font-bold text-base sm:text-lg mb-4">
                  We are at UCW
                </h4>
                <ul className="flex flex-col gap-2 sm:gap-4 text-xs sm:text-sm text-white">
                  {weAreAtUCW.map((link, index) => (
                    <li key={index}>
                      <a
                        href={link.href}
                        target="_blank"
                        className="hover:text-red-600"
                      >
                        {link.title}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="lg:col-span-1">
                <h4 className="font-bold text-base sm:text-lg mb-4">
                  Study at UCW
                </h4>
                <ul className="flex flex-col gap-2 sm:gap-4 text-xs sm:text-sm text-white">
                  {studyAtUCW.map((link, index) => (
                    <li key={index}>
                      <a
                        href={link.href}
                        target="_blank"
                        className="hover:text-red-600"
                      >
                        {link.title}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            <div className="p-2 sm:p-4">
              <p className="text-xs sm:text-sm text-white"> UCW is part of:</p>
            </div>
            <div>
              <Image
                src="/ucwlogin/gus-logo.png"
                alt="University Canada West"
                width={150}
                height={40}
                className="mb-4 max-w-full h-auto max-sm:max-w-[150px] max-sm:h-auto"
              />
            </div>

            <div className="border-t border-white pt-6 sm:pt-8 flex flex-col justify-between gap-3 sm:gap-5">
              <div className="text-xs sm:text-sm text-white">
                {" "}
                We acknowledge that the territories on which UCW and its
                campuses are situated are the traditional, ancestral and unceded
                territories of the x&quot;mak&quot;ayam (Musqueam), Skwxwú7mesh
                (Squamish) and Selilwitulh/salilwatat (Tsleil-Waututh) Nations.
                We thank them for having cared for this land since time
                immemorial, honour their graciousness to the students who seek
                knowledge here, and iterate our dedication to valuing the
                ongoing contributions of Indigenous peoples and communities.
              </div>
              <div className="text-xs sm:text-sm text-white">
                © Copyright 2005 - 2025 | University Canada West. All rights
                reserved. Learningwise Education Inc.
              </div>
              <div className="flex flex-row gap-4 sm:gap-7 mt-4 md:mt-0">
                <a
                  href="https://www.ucanwest.ca/privacy-policy#"
                  target="_blank"
                  className="text-xs sm:text-sm text-white hover:text-red-600"
                >
                  Privacy Policies
                </a>
                <a
                  href="https://www.ucanwest.ca/terms-and-conditions"
                  target="_blank"
                  className="text-xs sm:text-sm text-white hover:text-red-600"
                >
                  Terms & Conditions
                </a>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
}
