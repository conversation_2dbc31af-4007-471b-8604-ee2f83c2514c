"use client";
import { fetchAuthSession } from "aws-amplify/auth";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import {
  CognitoAccessToken,
  CognitoIdToken,
  CognitoRefreshToken,
  CognitoUser,
  CognitoUserPool,
  CognitoUserSession,
} from "amazon-cognito-identity-js";
import { getAwsConfiguration } from "@/helpers/getAwsConfig";

export default function Page() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const utmSource = searchParams?.get("utm_source") || null;
  const utmMedium = searchParams?.get("utm_medium") || null;
  const utmCampaign = searchParams?.get("utm_campaign") || null;
  const utmContent = searchParams?.get("utm_content") || null;
  const utmTerm = searchParams?.get("utm_term") || null;
  const utmNetwork = searchParams?.get("utm_network") || null;
  const utmReferrer = searchParams?.get("utm_referrer") || null;
  const isIframe = searchParams?.get("fromAppHero") === "true";

  const messageReceivedRef = useRef(false);

  useEffect(() => {
    if (typeof window !== "undefined" && searchParams) {
      const utmParams: Record<string, string> = {};

      if (utmSource) utmParams.utmSource = utmSource;
      if (utmMedium) utmParams.utmMedium = utmMedium;
      if (utmCampaign) utmParams.utmCampaign = utmCampaign;
      if (utmContent) utmParams.utmContent = utmContent;
      if (utmTerm) utmParams.utmTerm = utmTerm;
      if (utmNetwork) utmParams.utmNetwork = utmNetwork;
      if (utmReferrer) utmParams.utmReferrer = utmReferrer;

      // Only store if there are any UTM parameters
      if (Object.keys(utmParams).length > 0) {
        console.log("setting utm params", utmParams);
        sessionStorage.setItem("utmParams", JSON.stringify(utmParams));
      }
    }
  }, [
    utmSource,
    utmMedium,
    utmCampaign,
    utmContent,
    utmTerm,
    utmNetwork,
    utmReferrer,
    searchParams,
  ]);

  // Function to set up Amplify session with tokens
  const setupAmplifySession = async (
    username: string,
    accessToken: string,
    idToken: string,
    refreshToken: string,
    userPoolId: string,
    appClientId: string
  ) => {
    try {
      // Create Cognito tokens
      const accessCognitoToken = new CognitoAccessToken({
        AccessToken: accessToken,
      });
      const idCognitoToken = new CognitoIdToken({
        IdToken: idToken,
      });
      const refreshCognitoToken = new CognitoRefreshToken({
        RefreshToken: refreshToken,
      });

      // Create a new user session with the tokens
      const session = new CognitoUserSession({
        AccessToken: accessCognitoToken,
        IdToken: idCognitoToken,
        RefreshToken: refreshCognitoToken,
      });

      // Create a user pool object
      const userPool = new CognitoUserPool({
        UserPoolId: userPoolId,
        ClientId: appClientId,
      });

      // Create a new user object from that pool
      const user = new CognitoUser({
        Username: username,
        Pool: userPool,
      });

      // Connect user to the session
      user.setSignInUserSession(session);

      console.log("Successfully set up Amplify session with tokens");
      return true;
    } catch (error) {
      console.error("Error setting up Amplify session:", error);
      return false;
    }
  };
  useEffect(() => {
    if (!isIframe) return;

    // Function to send iframe ready message to parent window
    const sendIframeReadyMessage = () => {
      try {
        const message = {
          type: "ready_state",
          timestamp: new Date().getTime(),
        };

        // Send message to parent window
        if (window.parent && window.parent !== window) {
          window.parent.postMessage(message, "https://stage.apphero.io");
          console.log("Sent ready_state message to parent window");
        }
      } catch (error) {
        console.error("Error sending iframe ready message:", error);
      }
    };

    // Send the ready message after component has mounted
    // Use a small delay to ensure the iframe is fully loaded

    const readyTimer = setTimeout(() => {
      console.log("Sending iframe ready message after 100 ms");
      sendIframeReadyMessage();
    }, 100);

    return () => {
      clearTimeout(readyTimer);
    };
  }, [isIframe]);

  useEffect(() => {
    if (!isIframe) return;

    // Flag to prevent multiple processing
    let isProcessingCode = false;

    // Handler for message events containing authorization code
    const handleMessageEvent = async (event: any) => {
      console.log("Message event received:", event.data);

      // Security check: Validate the origin of the message
      // You should replace 'https://trusted-parent-domain.com' with your actual trusted domain
      const trustedOrigins = [
        "https://stage.apphero.io",
        "http://localhost:3001",
        "*",
      ];

      if (!trustedOrigins.includes(event.origin)) {
        console.error("Message received from untrusted origin:", event.origin);
        return;
      }

      // Check if we're already processing a code or if the event is invalid
      if (isProcessingCode || !event || !event.data) {
        console.log(
          "Skipping event processing: already processing or invalid event"
        );
        return;
      }

      if (event?.data?.type === "AUTH_REQUEST") {
        try {
          console.log("Check Existing authentication");
          const { tokens } = await fetchAuthSession();

          if (tokens?.accessToken) {
            console.log(
              "Already authenticated, redirecting to application-filter"
            );
            router.push("application-filter");
            return;
          }
        } catch (error) {
          console.log(
            "No existing session, will try to authenticate with code"
          );
        }
      } else {
        router.push("/login");
        return;
      }

      try {
        isProcessingCode = true;
        const code = event.data;

        // Exchange the authorization code for tokens
        try {
          const awsConfig = await getAwsConfiguration();

          const clientId = awsConfig.aws_user_pools_web_client_id;
          const userPoolId = awsConfig.aws_user_pools_id;

          // Extract tokens from response
          const { access_token, id_token, refresh_token } = code;
          console.log("Processing AUTH_REQUEST with tokens");

          // Get username from the ID token payload
          const payload = JSON.parse(atob(id_token?.split(".")[1]));
          const username = payload.email || payload.sub;

          // Set up the Amplify session with the tokens
          const sessionSetup = await setupAmplifySession(
            username,
            access_token,
            id_token,
            refresh_token,
            userPoolId,
            clientId
          );

          if (sessionSetup) {
            // Redirect to application filter page
            localStorage.setItem(
              "basic-details",
              JSON.stringify({ email: username })
            );
            localStorage.setItem("email", username);
            router.push("/application-filter");
          } else {
            console.log("Failed to set up session", sessionSetup);
            throw new Error("Failed to set up session");
          }
        } catch (tokenError) {
          console.log("Error exchanging code for tokens:", tokenError);
          router.push("/login");
        }
      } catch (eventError) {
        router.push("/login");
      } finally {
        isProcessingCode = false;
      }
    };

    // Create a named event handler function for proper cleanup
    const messageEventHandler = (event: any) => {
      // Mark that we've received a message event
      messageReceivedRef.current = true;

      if (event.data?.type === "AUTH_REQUEST") {
        console.log("AUTH_REQUEST detected, calling handleMessageEvent");
        handleMessageEvent(event);
      }
    };

    if (typeof window !== "undefined") {
      window.addEventListener("message", messageEventHandler);
    }

    return () => {
      if (typeof window !== "undefined") {
        window.removeEventListener("message", messageEventHandler);
      }
    };
  }, [router, isIframe]);

  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const checkAuthDirectly = async () => {
        console.log("Checking authentication directly");
        try {
          const { tokens } = await fetchAuthSession();
          if (tokens?.accessToken) {
            router.push("application-filter");
          } else {
            router.push("/login");
          }
        } catch (error) {
          router.push("/login");
        }
      };

      // Update countdown every second
      const countdownInterval = setInterval(() => {
        setCountdown((prevCount: number) => {
          if (prevCount <= 1) {
            clearInterval(countdownInterval);
            return 0;
          }
          return prevCount - 1;
        });
      }, 1000);

      const fallbackTimer = () => {
        if (!isIframe && !messageReceivedRef.current) {
          checkAuthDirectly();
        }
      };

      if (!isIframe) {
        checkAuthDirectly();
      } else if (isIframe) {
        setTimeout(fallbackTimer, countdown * 1000);
      }

      return () => {
        clearInterval(countdownInterval);
      };
    }
  }, [router, isIframe]);

  return (
    <main className="min-h-screen bg-primary flex flex-col items-center justify-center overflow-scroll">
      <div className="text-white text-center">
        <h1 className="text-2xl mb-4">Authentication in progress...</h1>
        <p>
          {isIframe
            ? "Waiting for authentication data from parent window..."
            : "Please wait while we authenticate your session."}
        </p>
        <div className="mt-3 mb-3">
          <div className="inline-block px-4 py-2 bg-white/10 rounded-full">
            <span className="text-white font-medium">
              Redirecting in {countdown} seconds
            </span>
          </div>
        </div>
        <p className="mt-4 text-sm">
          If you&apos;re not redirected automatically,{" "}
          <button
            onClick={() => router.push("/login")}
            className="underline text-blue-300 hover:text-blue-100"
          >
            click here to login
          </button>
        </p>
      </div>
    </main>
  );
}
