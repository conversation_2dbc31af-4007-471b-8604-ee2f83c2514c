import { atomWithStorage } from "jotai/utils";

// Define the structure of static contents
interface StaticContents {
  errors?: {
    userValidation?: {
      passwordMismatch?: string;
      passwordRequirements?: string;
      [key: string]: any;
    };
    [key: string]: any;
  };
  [key: string]: any;
}

export const nextForm = atomWithStorage("next-form", {});
export const email = atomWithStorage("email", "");
export const applicationId = atomWithStorage("applicationId", "");
export const routes = atomWithStorage("routes", "");
export const brandLogo = atomWithStorage("brandLogo", "");
export const programmeName = atomWithStorage("programmeName", "");
export const consumerAPIKey = atomWithStorage("consumerAPIKey", "");
export const preferredDateFormat = atomWithStorage("preferredDateFormat", "");
export const qualifyingQuestions = atomWithStorage("qualifying-questions", {});
export const dateReplacement = atomWithStorage("dateReplacement", "");
export const preferredLanguage = atomWithStorage("preferredLanguage", "en");
export const staticContentsAtom = atomWithStorage<StaticContents>(
  "staticContents",
  {}
);
export const recaptchaEnabledAtom = atomWithStorage("recaptchaEnabled", false);

export const fontSizeAtom = atomWithStorage<any>("fontSize", {});
